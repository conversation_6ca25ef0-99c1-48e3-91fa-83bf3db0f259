const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const dotenv = require('dotenv');
const nodemailer = require('nodemailer');

// Cargar variables de entorno
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Rutas
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: 'Server is running' });
});

// Ruta para enviar correos de contacto
app.post('/api/contact', async (req, res) => {
  const { name, email, phone, service, message } = req.body;
  
  if (!name || !email || !message) {
    return res.status(400).json({ 
      success: false, 
      message: 'Por favor, completa todos los campos requeridos' 
    });
  }
  
  try {
    // Configuración del transporter de nodemailer
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: true,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });
    
    // Email para el administrador
    const adminMailOptions = {
      from: `"TechFix" <${process.env.EMAIL_USER}>`,
      to: process.env.EMAIL_USER,
      subject: `Nuevo mensaje de contacto de ${name}`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body {
              font-family: 'Arial', sans-serif;
              line-height: 1.6;
              color: #333;
            }
            .container {
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
              background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
              border-radius: 10px;
            }
            .header {
              background: linear-gradient(135deg, #4338ca 0%, #6366f1 100%);
              color: white;
              padding: 20px;
              text-align: center;
              border-radius: 10px 10px 0 0;
              margin-bottom: 20px;
            }
            .content {
              padding: 20px;
              background-color: rgba(255, 255, 255, 0.8);
              border-radius: 10px;
            }
            .footer {
              text-align: center;
              margin-top: 20px;
              font-size: 12px;
              color: #666;
            }
            h1 {
              color: white;
              margin: 0;
            }
            h2 {
              color: #4338ca;
            }
            .detail {
              margin-bottom: 10px;
            }
            .label {
              font-weight: bold;
              color: #4338ca;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Nuevo Mensaje de Contacto</h1>
            </div>
            <div class="content">
              <h2>Detalles del mensaje:</h2>
              <div class="detail">
                <span class="label">Nombre:</span> ${name}
              </div>
              <div class="detail">
                <span class="label">Email:</span> ${email}
              </div>
              <div class="detail">
                <span class="label">Teléfono:</span> ${phone || 'No proporcionado'}
              </div>
              <div class="detail">
                <span class="label">Servicio:</span> ${service || 'No seleccionado'}
              </div>
              <div class="detail">
                <span class="label">Mensaje:</span>
                <p>${message}</p>
              </div>
            </div>
            <div class="footer">
              <p>Este es un mensaje automático de TechFix. Por favor no responda a este correo.</p>
            </div>
          </div>
        </body>
        </html>
      `
    };
    
    // Email para el cliente
    const clientMailOptions = {
      from: `"TechFix" <${process.env.EMAIL_USER}>`,
      to: email,
      subject: `Gracias por contactar con TechFix`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body {
              font-family: 'Arial', sans-serif;
              line-height: 1.6;
              color: #333;
            }
            .container {
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
              background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
              border-radius: 10px;
            }
            .header {
              background: linear-gradient(135deg, #4338ca 0%, #6366f1 100%);
              color: white;
              padding: 20px;
              text-align: center;
              border-radius: 10px 10px 0 0;
              margin-bottom: 20px;
            }
            .content {
              padding: 20px;
              background-color: rgba(255, 255, 255, 0.8);
              border-radius: 10px;
            }
            .footer {
              text-align: center;
              margin-top: 20px;
              font-size: 12px;
              color: #666;
            }
            h1 {
              color: white;
              margin: 0;
            }
            h2 {
              color: #4338ca;
            }
            .button {
              display: inline-block;
              padding: 10px 20px;
              background: linear-gradient(135deg, #4338ca 0%, #6366f1 100%);
              color: white;
              text-decoration: none;
              border-radius: 5px;
              margin-top: 15px;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Gracias por contactar con TechFix</h1>
            </div>
            <div class="content">
              <h2>Hola ${name},</h2>
              <p>Hemos recibido tu mensaje y te agradecemos por contactarnos.</p>
              <p>Un resumen de tu consulta:</p>
              <ul>
                <li><strong>Servicio solicitado:</strong> ${service || 'No especificado'}</li>
                <li><strong>Mensaje:</strong> "${message}"</li>
              </ul>
              <p>Nos pondremos en contacto contigo lo antes posible para brindarte la mejor solución a tus necesidades.</p>
              <p>Si tienes alguna pregunta adicional, no dudes en llamarnos al (54) 1125987142.</p>
              <div style="text-align: center;">
                <a href="https://techfix.com" class="button">Visitar nuestra web</a>
              </div>
            </div>
            <div class="footer">
              <p>© ${new Date().getFullYear()} TechFix - Ignacio Rocha. Todos los derechos reservados.</p>
            </div>
          </div>
        </body>
        </html>
      `
    };
    
    // Enviar ambos correos
    await transporter.sendMail(adminMailOptions);
    await transporter.sendMail(clientMailOptions);
    
    res.status(200).json({ 
      success: true, 
      message: 'Mensaje enviado con éxito' 
    });
  } catch (error) {
    console.error('Error al enviar el mensaje:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Error al enviar el mensaje. Por favor, intenta nuevamente.' 
    });
  }
});

// Ruta para obtener servicios
app.get('/api/services', (req, res) => {
  const services = [
    {
      id: 1,
      title: 'Reparación de Hardware',
      description: 'Soluciones para problemas de hardware como pantallas rotas, baterías defectuosas, discos duros dañados y más.',
      icon: 'FaTools'
    },
    {
      id: 2,
      title: 'Solución de Software',
      description: 'Resolución de problemas de sistema operativo, instalación de programas, actualizaciones y optimización.',
      icon: 'FaLaptop'
    },
    {
      id: 3,
      title: 'Eliminación de Virus',
      description: 'Detección y eliminación de malware, virus, spyware y otras amenazas que afectan el rendimiento.',
      icon: 'FaShieldAlt'
    },
    {
      id: 4,
      title: 'Actualización de Componentes',
      description: 'Mejora el rendimiento de tu computadora con actualizaciones de hardware como memoria RAM, disco duro y más.',
      icon: 'FaMemory'
    },
    {
      id: 5,
      title: 'Recuperación de Datos',
      description: 'Recuperación de archivos importantes de discos duros dañados, memorias USB corruptas o sistemas que no arrancan.',
      icon: 'FaDatabase'
    },
    {
      id: 6,
      title: 'Mantenimiento Preventivo',
      description: 'Servicio de mantenimiento regular para prevenir problemas futuros y mantener tu equipo funcionando de manera óptima.',
      icon: 'FaWrench'
    }
  ];
  
  res.status(200).json({ success: true, data: services });
});

// Ruta para obtener testimonios
app.get('/api/testimonials', (req, res) => {
  const testimonials = [
    {
      id: 1,
      name: 'Carlos Rodríguez',
      role: 'Diseñador Gráfico',
      image: 'https://randomuser.me/api/portraits/men/32.jpg',
      content: 'Increíble servicio. Mi laptop dejó de funcionar justo antes de una entrega importante y la repararon en tiempo récord. Muy profesional y eficiente.',
      rating: 5
    },
    {
      id: 2,
      name: 'Laura Martínez',
      role: 'Estudiante Universitaria',
      image: 'https://randomuser.me/api/portraits/women/44.jpg',
      content: 'Excelente atención y muy buen precio. Me explicaron detalladamente el problema de mi computadora y las opciones para solucionarlo. Totalmente recomendado.',
      rating: 5
    },
    {
      id: 3,
      name: 'Miguel Ángel Pérez',
      role: 'Contador',
      image: 'https://randomuser.me/api/portraits/men/67.jpg',
      content: 'Servicio de primera calidad. Repararon mi PC que tenía problemas de sobrecalentamiento y ahora funciona perfectamente. Muy satisfecho con el resultado.',
      rating: 4
    }
  ];
  
  res.status(200).json({ success: true, data: testimonials });
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log(`Servidor corriendo en el puerto ${PORT}`);
});